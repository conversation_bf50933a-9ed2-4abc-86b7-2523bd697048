/**
 * اختبار نظام المسودات المستقلة عن الأرقام التسلسلية
 * Independent Draft System Test
 * 
 * يختبر هذا الملف:
 * 1. إنشاء مسودات مستقلة عن الأرقام التسلسلية
 * 2. حفظ المسودات تلقائياً بدون ربطها بالأرقام
 * 3. تحويل المسودات إلى تصفيات مكتملة
 * 4. التأكد من عدم الاعتماد على الأرقام التسلسلية في الفلترة
 */

// Test independent draft system
async function testIndependentDraftSystem() {
    console.log('🧪 [TEST] بدء اختبار نظام المسودات المستقلة...');
    
    const testResults = {
        draftCreation: false,
        draftAutoSave: false,
        draftCompletion: false,
        sequentialIndependence: false,
        filteringWithoutSequential: false
    };

    try {
        // Test 1: Create independent draft
        console.log('📝 [TEST] اختبار إنشاء مسودة مستقلة...');
        
        const testCashierId = 1;
        const testAccountantId = 1;
        const testDate = new Date().toISOString().split('T')[0];
        
        const draftResult = await ipcRenderer.invoke('create-independent-draft', {
            cashierId: testCashierId,
            accountantId: testAccountantId,
            reconciliationDate: testDate
        });
        
        if (draftResult && draftResult.draftUuid && draftResult.isIndependent) {
            testResults.draftCreation = true;
            console.log('✅ [TEST] تم إنشاء المسودة المستقلة بنجاح:', draftResult.draftUuid);
        }

        // Test 2: Auto-save draft data
        console.log('💾 [TEST] اختبار الحفظ التلقائي للمسودة...');
        
        const testDraftData = {
            systemSales: 1000,
            totalReceipts: 1200,
            surplusDeficit: 200,
            lastSaved: new Date().toISOString()
        };
        
        const saveResult = await ipcRenderer.invoke('save-draft-data', {
            draftUuid: draftResult.draftUuid,
            draftData: testDraftData
        });
        
        if (saveResult) {
            testResults.draftAutoSave = true;
            console.log('✅ [TEST] تم حفظ بيانات المسودة تلقائياً');
        }

        // Test 3: Complete draft reconciliation
        console.log('🏁 [TEST] اختبار تحويل المسودة إلى تصفية مكتملة...');
        
        const completionResult = await ipcRenderer.invoke('complete-draft-reconciliation', {
            reconciliationId: draftResult.id,
            systemSales: testDraftData.systemSales,
            totalReceipts: testDraftData.totalReceipts,
            surplusDeficit: testDraftData.surplusDeficit
        });
        
        if (completionResult) {
            testResults.draftCompletion = true;
            console.log('✅ [TEST] تم تحويل المسودة إلى تصفية مكتملة');
        }

        // Test 4: Verify sequential independence
        console.log('🔢 [TEST] اختبار الاستقلالية عن الأرقام التسلسلية...');
        
        // Check that draft UUID is not dependent on sequential ID
        const uuidPattern = /^draft_[a-z0-9]+_[a-z0-9]+$/;
        if (uuidPattern.test(draftResult.draftUuid)) {
            testResults.sequentialIndependence = true;
            console.log('✅ [TEST] المسودة مستقلة عن الأرقام التسلسلية');
        }

        // Test 5: Filtering without sequential dependency
        console.log('🔍 [TEST] اختبار الفلترة بدون الاعتماد على الأرقام التسلسلية...');
        
        // Query reconciliations by status instead of sequential ID
        const draftReconciliations = await ipcRenderer.invoke('db-all',
            'SELECT * FROM reconciliations WHERE status = ? AND is_draft_independent = 1',
            ['draft']
        );
        
        const completedReconciliations = await ipcRenderer.invoke('db-all',
            'SELECT * FROM reconciliations WHERE status = ? AND draft_uuid IS NOT NULL',
            ['completed']
        );
        
        if (Array.isArray(draftReconciliations) && Array.isArray(completedReconciliations)) {
            testResults.filteringWithoutSequential = true;
            console.log('✅ [TEST] الفلترة تعمل بدون الاعتماد على الأرقام التسلسلية');
            console.log(`📊 [TEST] المسودات: ${draftReconciliations.length}, المكتملة: ${completedReconciliations.length}`);
        }

    } catch (error) {
        console.error('❌ [TEST] خطأ في اختبار نظام المسودات المستقلة:', error);
    }

    // Display test results
    console.log('📋 [TEST] نتائج اختبار نظام المسودات المستقلة:');
    console.log('  - إنشاء المسودة المستقلة:', testResults.draftCreation ? '✅' : '❌');
    console.log('  - الحفظ التلقائي للمسودة:', testResults.draftAutoSave ? '✅' : '❌');
    console.log('  - تحويل المسودة إلى مكتملة:', testResults.draftCompletion ? '✅' : '❌');
    console.log('  - الاستقلالية عن الأرقام التسلسلية:', testResults.sequentialIndependence ? '✅' : '❌');
    console.log('  - الفلترة بدون أرقام تسلسلية:', testResults.filteringWithoutSequential ? '✅' : '❌');

    const allTestsPassed = Object.values(testResults).every(result => result === true);
    
    if (allTestsPassed) {
        console.log('🎉 [TEST] جميع اختبارات نظام المسودات المستقلة نجحت!');
        DialogUtils.showSuccess(
            'تم اختبار نظام المسودات المستقلة بنجاح!\n\n' +
            '✅ إنشاء المسودات المستقلة\n' +
            '✅ الحفظ التلقائي للمسودات\n' +
            '✅ تحويل المسودات إلى تصفيات مكتملة\n' +
            '✅ الاستقلالية عن الأرقام التسلسلية\n' +
            '✅ الفلترة بدون اعتماد على الأرقام التسلسلية',
            'نجح اختبار نظام المسودات المستقلة'
        );
    } else {
        console.log('⚠️ [TEST] بعض اختبارات نظام المسودات المستقلة فشلت');
        DialogUtils.showWarning(
            'تم اكتشاف مشاكل في نظام المسودات المستقلة:\n\n' +
            `${testResults.draftCreation ? '✅' : '❌'} إنشاء المسودة المستقلة\n` +
            `${testResults.draftAutoSave ? '✅' : '❌'} الحفظ التلقائي للمسودة\n` +
            `${testResults.draftCompletion ? '✅' : '❌'} تحويل المسودة إلى مكتملة\n` +
            `${testResults.sequentialIndependence ? '✅' : '❌'} الاستقلالية عن الأرقام التسلسلية\n` +
            `${testResults.filteringWithoutSequential ? '✅' : '❌'} الفلترة بدون أرقام تسلسلية\n\n` +
            'يرجى مراجعة السجلات للحصول على تفاصيل أكثر.',
            'تحذير: مشاكل في نظام المسودات المستقلة'
        );
    }

    return testResults;
}

// Test draft filtering functionality
async function testDraftFiltering() {
    console.log('🔍 [TEST] بدء اختبار فلترة المسودات...');
    
    try {
        // Test filtering drafts by status
        const drafts = await ipcRenderer.invoke('db-all',
            'SELECT * FROM reconciliations WHERE status = ? ORDER BY created_at DESC',
            ['draft']
        );
        
        // Test filtering completed reconciliations
        const completed = await ipcRenderer.invoke('db-all',
            'SELECT * FROM reconciliations WHERE status = ? ORDER BY updated_at DESC',
            ['completed']
        );
        
        // Test filtering independent drafts specifically
        const independentDrafts = await ipcRenderer.invoke('db-all',
            'SELECT * FROM reconciliations WHERE is_draft_independent = 1 ORDER BY created_at DESC'
        );
        
        console.log('📊 [TEST] نتائج فلترة المسودات:');
        console.log(`  - إجمالي المسودات: ${drafts.length}`);
        console.log(`  - إجمالي المكتملة: ${completed.length}`);
        console.log(`  - المسودات المستقلة: ${independentDrafts.length}`);
        
        // Display sample data
        if (independentDrafts.length > 0) {
            console.log('📝 [TEST] عينة من المسودات المستقلة:');
            independentDrafts.slice(0, 3).forEach((draft, index) => {
                console.log(`  ${index + 1}. UUID: ${draft.draft_uuid}, التاريخ: ${draft.created_at}`);
            });
        }
        
        return {
            totalDrafts: drafts.length,
            totalCompleted: completed.length,
            independentDrafts: independentDrafts.length
        };
        
    } catch (error) {
        console.error('❌ [TEST] خطأ في اختبار فلترة المسودات:', error);
        return null;
    }
}

// Export test functions for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        testIndependentDraftSystem,
        testDraftFiltering
    };
}
